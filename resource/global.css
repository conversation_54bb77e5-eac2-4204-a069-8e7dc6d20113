body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
}
td,th {
	line-height:18px;
}

div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,blockquote,pre,form,input,textarea {
	margin: 0px;
	padding: 0px;
}

h1, h2, h3, h4, h5, h6 {
	font-size:100%;
	font-weight:bold;
}

.clear {clear: both; font-size: 0px; line-height: 0px; height:0;}
p {margin:0px;padding:5px 0px;}
ol,ul {	list-style: none;}

a { text-decoration:none; color:#0782C1}
a:hover {  color:#0782C1; text-decoration: none; }

img { border : 0px; max-width: 100%;}
input {   color: #2a2a2a;}
.input-warn {border:1px solid #FF0000;line-height:20px;	height:18px;}
.input-ok { border:1px solid #00CC66; line-height:20px;	height:18px;}
.button {
	background:url(../images/button_bg.gif) center no-repeat;
	cursor:pointer;
	border:none;
	color:#000;
	padding: 5px 2px;
	width:103px ;

}

/* OPTIONAL BUTTON STYLES for applying custom look and feel: */
button.btn {
	padding:0px 10px;
	height:24px;
	border:1px solid #d4d4d4;
	background:#fefefe;
	background-image:-moz-linear-gradient(top,#fefefe,#ebebeb);
	background-image:-webkit-gradient(linear,left top,left bottom,from(#fefefe),to(#ebebeb));
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr=#fefefe,endColorStr=#ebebeb);
	-ms-filter:"progid:DXImageTransform.Microsoft.Gradient(startColorStr=#fefefe, endColorStr=#ebebeb)";
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
	border-radius:3px;
	white-space:nowrap;
	vertical-align:middle;
	cursor:pointer;
	overflow:visible;
}
button.btn span {
	display:inline-block;
	vertical-align:middle;
	color:#656565;
	font-size:13px;
}
button.btn:hover, button.btn:focus{
	border-color:#d4d4d4;background:#ebebeb;
	background-image:-moz-linear-gradient(top,#ebebeb,#fefefe);
	background-image:-webkit-gradient(linear,left top,left bottom,from(#ebebeb),to(#fefefe));
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr=#ebebeb,endColorStr=#fefefe);
	-ms-filter:"progid:DXImageTransform.Microsoft.Gradient(startColorStr=#ebebeb, endColorStr=#fefefe)";
	outline:0;-moz-box-shadow:0 0 3px #999;
	-webkit-box-shadow:0 0 3px #999;
	box-shadow:0 0 3px #999
}

.btn-default { background:#09459f; color:#ffffff ; border-color: #073f93; text-transform:uppercase; font-weight:bold; height:30px; line-height:18px; }
.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
	color: #ffffff;
	background-color: #4e4e4e;
	border-color: #4e4e4e;
}




.textfiled {
	border:1px solid #b7b7b7;
	background:#efefef;
	color:#2a2a2a;
	border-bottom:none;
	border-right:none;
	line-height:20px;
	height:20px;
	font-size:12px;

}
.textarea{
	border:1px solid #b7b7b7;
	border-bottom:none;
	border-right:none;
	background:#efefef;
	color:#2a2a2a;
	line-height:20px;
	overflow:auto;

}
.select{
	border:1px solid #b7b7b7;
	border-bottom:none;
	border-right:none;
	background:#efefef;
	color:#2a2a2a;
	padding:3px;
	font-size:11px;

}


.clearfix:after
{
	content: '[DO NOT LEAVE IT IS NOT REAL]';
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.clearfix{
	display: inline;
	position: relative;
}
/*\*/
.clearfix	{
	display: block;
}

.fl {float:left;}
.fr {float:right;}
.hidden {display:none;}


.inner-addon {
	position: relative;
}

/* style glyph */
.inner-addon .glyphicon {
	position: absolute;
	padding: 10px;
	pointer-events: none;
	color:#a1a1a1;
}

/* align glyph */
.left-addon .glyphicon  { left:  0px;}
.right-addon .glyphicon { right: 0px;}

/* add padding  */
.left-addon input  { padding-left:  30px; }
.right-addon input { padding-right: 30px; }
.left-addon textarea { padding-left:  30px;}

/**/
.hide-on-biggest { 	display: none;}

/* -------------------------------------------------------------------
 format text
------------------------------------------------------------------- */

.font_err {	color:#FF0000;}
.font_err a:active , .font_err a:link , .font_err a:visited {  text-decoration: none ; color:#FF0000}
.font_err a:hover {  color:#FF0000; text-decoration: underline; }


/********************* CSS for box_redirect ****************************/
#box_redirect{ border:1px solid #505050; width:70% ;margin:0px auto;  }
#box_redirect p { line-height:18px;}
#box_redirect .top { background:#F78600;border-bottom:1px solid #5C5C5C; color:#FFFFFF;font-size:11px;font-weight:bold;text-transform:uppercase; height:22px;padding:2px; text-align:left; }
#box_redirect .fontMess {color:#65D700;font-size:12px; font-weight:bold;text-align:center;padding:20px 10px;}
#box_redirect .mess { background:#383838; color:#FFFFFF;text-align:center}
#box_redirect .bottom { background:#FFF3DD; color:#000;text-align:center;padding:5px; font-size:12px;}

/********************* CSS for boxMess ****************************/
#boxMess{  border:1px solid #F08200;padding:2px 5px; background:#FCF5CD }
#boxMess  h4.mess { background:url(../images/icon/icon_mess.gif) no-repeat left; padding:2px 30px; color:#FF0000; font-weight:bold; font-size:12px;}
#boxMess  h4.err { background:url(../images/icon/icon_warning.gif) no-repeat left; padding:6px 30px; color:#FF0000; font-weight:bold;}
#boxMess .font_err { color:#950000;}



/******************** PHAN TRANG **************/
.pagination{
	background:url(../images/hr.gif) top repeat-x;
	padding-top:10px;
	text-align:center;
}
.pagination .pagetotal {
	background-color:#868686;
	color:#FFFFFF;
	padding:2px 3px;
}
.pagination a, .pagination a:visited{
	padding: 0px 2px;
	display:inline-block ;
	text-decoration: none;
	color: #868686;
	font-weight:bold;
	background:url(../images/page_bg.gif) center no-repeat;
	width:18px;
	height:15px;
	line-height:15px;
}

.pagination a:hover, .pagination a:active{
	color: #3866bb;
	text-decoration:none;
}

.pagination .pagecur{
	font-weight: bold;
	display:inline-block ;
	padding: 0px 2px;
	background:url(../images/page_on.gif) center no-repeat;
	color: #fff;
	width:18px;
	height:15px;
	line-height:15px;
}

.pagination .btnPage{
	color:#868686;
	text-align:center;
	font-weight:bold;
}
.pagination a:visited.btnPage  {
	color:#868686;
	font-weight:bold;
}